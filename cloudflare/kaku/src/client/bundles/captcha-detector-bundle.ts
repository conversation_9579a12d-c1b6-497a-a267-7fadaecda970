import { InterceptorRegistry } from '../interceptors/interceptor-registry';
import VideoFrameInterceptor from '../interceptors/video-crop-interceptor';
import ChangeDetectorInterceptor from '../interceptors/change-detector-interceptor';

// Register interceptors
if (!window.interceptorRegistry) {
  let interceptorRegistry = new InterceptorRegistry();
  // Register VideoFrameInterceptor (video-crop)
  interceptorRegistry.register('video-crop', VideoFrameInterceptor, {
    debug: true,
    enableCropping: true,
  });
  interceptorRegistry.register('change-detector', ChangeDetectorInterceptor, {
    debug: true,
    enableCropping: true,
  });

  window.interceptorRegistry = interceptorRegistry;
} else {
  console.warn('[captcha-detector-bundle] InterceptorRegistry already exists');
}
